@echo off
echo ========================================
echo Installing Voice to Text App Dependencies
echo ========================================

echo.
echo Step 1: Upgrading pip...
python -m pip install --upgrade pip

echo.
echo Step 2: Installing PyTorch (CPU version)...
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu

echo.
echo Step 3: Installing Whisper...
pip install openai-whisper

echo.
echo Step 4: Installing audio dependencies...
pip install pyaudio

echo.
echo Step 5: Installing GUI dependencies...
pip install pyautogui

echo.
echo Step 6: Installing additional dependencies...
pip install numpy

echo.
echo ========================================
echo Installation completed!
echo ========================================
echo.
echo You can now run the application using:
echo python voice_to_text_app.py
echo.
pause
