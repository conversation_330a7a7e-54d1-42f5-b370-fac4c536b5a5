@echo off
echo ========================================
echo Installing Voice to Text App Dependencies
echo ========================================

echo.
echo Step 1: Upgrading pip and setuptools...
python -m pip install --upgrade pip setuptools wheel

echo.
echo Step 2: Installing PyTorch (CPU version)...
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu

echo.
echo Step 3: Installing basic dependencies...
pip install numpy

echo.
echo Step 4: Installing audio dependencies...
pip install pyaudio

echo.
echo Step 5: Installing GUI dependencies...
pip install pyautogui

echo.
echo Step 6: Installing Whisper (alternative method)...
pip install --no-deps openai-whisper
if errorlevel 1 (
    echo Trying alternative installation method...
    pip install git+https://github.com/openai/whisper.git
    if errorlevel 1 (
        echo Trying pip install with --force-reinstall...
        pip install --force-reinstall --no-cache-dir openai-whisper
    )
)

echo.
echo ========================================
echo Installation completed!
echo ========================================
echo.
echo You can now run the application using:
echo python voice_to_text_app.py
echo.
pause
