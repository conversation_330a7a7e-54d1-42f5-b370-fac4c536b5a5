import tkinter as tk
from tkinter import ttk, messagebox
import threading
import pyaudio
import wave
import whisper
import pyautogui
import os
import tempfile
import time

class VoiceToTextApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تحويل الكلام إلى نص - Voice to Text")
        self.root.geometry("450x400")
        self.root.resizable(False, False)
        
        # متغيرات التسجيل
        self.is_recording = False
        self.audio_frames = []
        self.audio_stream = None
        self.audio = None
        
        # إعدادات الصوت
        self.CHUNK = 1024
        self.FORMAT = pyaudio.paInt16
        self.CHANNELS = 1
        self.RATE = 16000
        
        # تحميل نموذج Whisper
        self.whisper_model = None
        self.load_whisper_model()
        
        self.setup_ui()
        
    def load_whisper_model(self):
        """تحميل نموذج Whisper"""
        def load_model():
            try:
                self.status_label.config(text="جاري تحميل نموذج Whisper...")
                self.root.update()
                self.whisper_model = whisper.load_model("base")
                self.status_label.config(text="جاهز للاستخدام")
                self.record_button.config(state="normal")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل نموذج Whisper: {str(e)}")
                self.status_label.config(text="خطأ في تحميل النموذج")

        # تحميل النموذج في خيط منفصل لتجنب تجميد الواجهة
        self.record_button.config(state="disabled")
        threading.Thread(target=load_model, daemon=True).start()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(self.root, text="تحويل الكلام إلى نص", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=20)
        
        # حالة التطبيق
        self.status_label = tk.Label(self.root, text="جاري التحميل...",
                                   font=("Arial", 10))
        self.status_label.pack(pady=5)

        # إعدادات اللغة
        lang_frame = tk.Frame(self.root)
        lang_frame.pack(pady=5)

        tk.Label(lang_frame, text="اللغة:", font=("Arial", 10)).pack(side="left")
        self.language_var = tk.StringVar(value="ar")
        language_combo = ttk.Combobox(lang_frame, textvariable=self.language_var,
                                    values=["ar", "en", "auto"], width=10, state="readonly")
        language_combo.pack(side="left", padx=5)

        # تلميحات اللغات
        lang_hints = {"ar": "عربي", "en": "English", "auto": "تلقائي"}
        language_combo.bind("<<ComboboxSelected>>",
                          lambda e: self.status_label.config(text=f"تم اختيار: {lang_hints[self.language_var.get()]}"))
        
        # زر التسجيل
        self.record_button = tk.Button(self.root, text="🎤 ابدأ التسجيل", 
                                     font=("Arial", 14), 
                                     bg="#4CAF50", fg="white",
                                     command=self.toggle_recording,
                                     width=15, height=2)
        self.record_button.pack(pady=20)
        
        # مؤشر التسجيل
        self.recording_indicator = tk.Label(self.root, text="", 
                                          font=("Arial", 12), fg="red")
        self.recording_indicator.pack(pady=5)
        
        # منطقة النص المحول
        text_frame = tk.Frame(self.root)
        text_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        tk.Label(text_frame, text="النص المحول:", font=("Arial", 10)).pack(anchor="w")
        
        self.text_display = tk.Text(text_frame, height=6, wrap=tk.WORD, 
                                  font=("Arial", 10))
        self.text_display.pack(fill="both", expand=True)
        
        # أزرار إضافية
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        self.copy_button = tk.Button(button_frame, text="نسخ النص", 
                                   command=self.copy_text)
        self.copy_button.pack(side="left", padx=5)
        
        self.send_button = tk.Button(button_frame, text="إرسال النص", 
                                   command=self.send_text)
        self.send_button.pack(side="left", padx=5)
        
        self.clear_button = tk.Button(button_frame, text="مسح", 
                                    command=self.clear_text)
        self.clear_button.pack(side="left", padx=5)
        
    def toggle_recording(self):
        """تبديل حالة التسجيل"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
            
    def start_recording(self):
        """بدء التسجيل"""
        try:
            self.audio = pyaudio.PyAudio()
            self.audio_stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                frames_per_buffer=self.CHUNK
            )
            
            self.is_recording = True
            self.audio_frames = []
            
            # تحديث واجهة المستخدم
            self.record_button.config(text="⏹️ إيقاف التسجيل", bg="#f44336")
            self.recording_indicator.config(text="🔴 جاري التسجيل...")
            self.status_label.config(text="جاري التسجيل...")
            
            # بدء التسجيل في خيط منفصل
            self.recording_thread = threading.Thread(target=self.record_audio)
            self.recording_thread.start()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في بدء التسجيل: {str(e)}")
            
    def record_audio(self):
        """تسجيل الصوت"""
        while self.is_recording:
            try:
                data = self.audio_stream.read(self.CHUNK)
                self.audio_frames.append(data)
            except Exception as e:
                print(f"خطأ في التسجيل: {e}")
                break
                
    def stop_recording(self):
        """إيقاف التسجيل"""
        self.is_recording = False
        
        if self.audio_stream:
            self.audio_stream.stop_stream()
            self.audio_stream.close()
        if self.audio:
            self.audio.terminate()
            
        # تحديث واجهة المستخدم
        self.record_button.config(text="🎤 ابدأ التسجيل", bg="#4CAF50")
        self.recording_indicator.config(text="")
        self.status_label.config(text="جاري معالجة الصوت...")
        
        # معالجة الصوت المسجل
        threading.Thread(target=self.process_audio).start()
        
    def process_audio(self):
        """معالجة الصوت وتحويله إلى نص"""
        try:
            if not self.audio_frames:
                self.status_label.config(text="لم يتم تسجيل أي صوت")
                return
                
            # حفظ الصوت في ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
            
            with wave.open(temp_file.name, 'wb') as wf:
                wf.setnchannels(self.CHANNELS)
                wf.setsampwidth(self.audio.get_sample_size(self.FORMAT))
                wf.setframerate(self.RATE)
                wf.writeframes(b''.join(self.audio_frames))
            
            # تحويل الصوت إلى نص باستخدام Whisper
            if self.whisper_model:
                language = self.language_var.get() if self.language_var.get() != "auto" else None
                result = self.whisper_model.transcribe(temp_file.name, language=language)
                text = result["text"].strip()
                
                if text:
                    # عرض النص في واجهة المستخدم
                    self.text_display.delete(1.0, tk.END)
                    self.text_display.insert(1.0, text)
                    self.status_label.config(text="تم التحويل بنجاح")
                else:
                    self.status_label.config(text="لم يتم التعرف على أي كلام")
            else:
                self.status_label.config(text="نموذج Whisper غير متاح")
                
            # حذف الملف المؤقت
            os.unlink(temp_file.name)
            
        except Exception as e:
            self.status_label.config(text=f"خطأ في المعالجة: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في معالجة الصوت: {str(e)}")
            
    def copy_text(self):
        """نسخ النص إلى الحافظة"""
        text = self.text_display.get(1.0, tk.END).strip()
        if text:
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            self.status_label.config(text="تم نسخ النص")
        else:
            messagebox.showwarning("تحذير", "لا يوجد نص للنسخ")
            
    def send_text(self):
        """إرسال النص إلى التطبيق النشط"""
        text = self.text_display.get(1.0, tk.END).strip()
        if text:
            try:
                # إخفاء النافذة مؤقتاً
                self.root.withdraw()
                time.sleep(0.5)  # انتظار قصير للتأكد من إخفاء النافذة
                
                # كتابة النص في التطبيق النشط
                pyautogui.typewrite(text)
                
                # إظهار النافذة مرة أخرى
                self.root.deiconify()
                self.status_label.config(text="تم إرسال النص")
                
            except Exception as e:
                self.root.deiconify()
                messagebox.showerror("خطأ", f"فشل في إرسال النص: {str(e)}")
        else:
            messagebox.showwarning("تحذير", "لا يوجد نص للإرسال")
            
    def clear_text(self):
        """مسح النص"""
        self.text_display.delete(1.0, tk.END)
        self.status_label.config(text="تم مسح النص")

def main():
    root = tk.Tk()
    app = VoiceToTextApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
