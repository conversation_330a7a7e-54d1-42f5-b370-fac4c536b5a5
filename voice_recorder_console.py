#!/usr/bin/env python3
"""
تطبيق تسجيل الصوت من سطر الأوامر
يعمل بدون واجهة رسومية في حالة وجود مشاكل في tkinter
"""

import pyaudio
import wave
import os
import time
import threading

class ConsoleVoiceRecorder:
    def __init__(self):
        # إعدادات الصوت
        self.CHUNK = 1024
        self.FORMAT = pyaudio.paInt16
        self.CHANNELS = 1
        self.RATE = 16000
        
        # متغيرات التسجيل
        self.is_recording = False
        self.audio_frames = []
        self.audio_stream = None
        self.audio = None
        
        print("=" * 50)
        print("تطبيق تسجيل الصوت - إصدار سطر الأوامر")
        print("=" * 50)
        
    def check_audio_devices(self):
        """فحص أجهزة الصوت المتاحة"""
        try:
            audio = pyaudio.PyAudio()
            device_count = audio.get_device_count()
            
            print(f"\nأجهزة الصوت المتاحة ({device_count}):")
            input_devices = []
            
            for i in range(device_count):
                device_info = audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:
                    input_devices.append(device_info['name'])
                    print(f"  {len(input_devices)}. {device_info['name']}")
            
            audio.terminate()
            
            if not input_devices:
                print("❌ لم يتم العثور على أجهزة إدخال صوتي!")
                return False
            else:
                print(f"✅ تم العثور على {len(input_devices)} جهاز إدخال صوتي")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في فحص أجهزة الصوت: {e}")
            return False
    
    def start_recording(self):
        """بدء التسجيل"""
        try:
            print("\n🎤 بدء التسجيل...")
            print("اضغط Enter لإيقاف التسجيل")
            
            self.audio = pyaudio.PyAudio()
            self.audio_stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                frames_per_buffer=self.CHUNK
            )
            
            self.is_recording = True
            self.audio_frames = []
            
            # بدء التسجيل في خيط منفصل
            self.recording_thread = threading.Thread(target=self.record_audio)
            self.recording_thread.start()
            
            # انتظار ضغط Enter
            input()
            
            # إيقاف التسجيل
            self.stop_recording()
            
        except Exception as e:
            print(f"❌ خطأ في بدء التسجيل: {e}")
            
    def record_audio(self):
        """تسجيل الصوت"""
        while self.is_recording:
            try:
                data = self.audio_stream.read(self.CHUNK)
                self.audio_frames.append(data)
            except Exception as e:
                print(f"خطأ في التسجيل: {e}")
                break
                
    def stop_recording(self):
        """إيقاف التسجيل"""
        print("⏹️ إيقاف التسجيل...")
        
        self.is_recording = False
        
        if self.audio_stream:
            self.audio_stream.stop_stream()
            self.audio_stream.close()
        if self.audio:
            self.audio.terminate()
            
        # حفظ الملف
        self.save_audio_file()
        
    def save_audio_file(self):
        """حفظ ملف الصوت"""
        try:
            if not self.audio_frames:
                print("❌ لم يتم تسجيل أي صوت")
                return
                
            # إنشاء اسم ملف فريد
            timestamp = int(time.time())
            filename = f"recording_{timestamp}.wav"
            
            print(f"💾 حفظ التسجيل في: {filename}")
            
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(self.CHANNELS)
                wf.setsampwidth(self.audio.get_sample_size(self.FORMAT))
                wf.setframerate(self.RATE)
                wf.writeframes(b''.join(self.audio_frames))
            
            print(f"✅ تم حفظ التسجيل بنجاح: {filename}")
            
            # عرض معلومات الملف
            file_size = os.path.getsize(filename)
            duration = len(self.audio_frames) * self.CHUNK / self.RATE
            
            print(f"📊 معلومات الملف:")
            print(f"   - الحجم: {file_size:,} بايت")
            print(f"   - المدة: {duration:.1f} ثانية")
            print(f"   - معدل العينات: {self.RATE} Hz")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ الملف: {e}")
    
    def run(self):
        """تشغيل التطبيق"""
        print("\n🔍 فحص أجهزة الصوت...")
        
        if not self.check_audio_devices():
            print("\n❌ لا يمكن المتابعة بدون جهاز إدخال صوتي")
            return
        
        while True:
            print("\n" + "=" * 30)
            print("الخيارات المتاحة:")
            print("1. بدء تسجيل جديد")
            print("2. عرض الملفات المسجلة")
            print("3. فحص أجهزة الصوت")
            print("4. خروج")
            print("=" * 30)
            
            choice = input("اختر رقم الخيار: ").strip()
            
            if choice == "1":
                self.start_recording()
            elif choice == "2":
                self.list_recordings()
            elif choice == "3":
                self.check_audio_devices()
            elif choice == "4":
                print("👋 شكراً لاستخدام التطبيق!")
                break
            else:
                print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")
    
    def list_recordings(self):
        """عرض قائمة الملفات المسجلة"""
        recordings = [f for f in os.listdir('.') if f.startswith('recording_') and f.endswith('.wav')]
        
        if not recordings:
            print("\n📁 لا توجد تسجيلات محفوظة")
            return
        
        print(f"\n📁 التسجيلات المحفوظة ({len(recordings)}):")
        for i, recording in enumerate(sorted(recordings), 1):
            file_size = os.path.getsize(recording)
            print(f"  {i}. {recording} ({file_size:,} بايت)")

def main():
    """الدالة الرئيسية"""
    try:
        # فحص pyaudio
        import pyaudio
        print("✅ PyAudio متاح")
    except ImportError:
        print("❌ PyAudio غير مثبت")
        print("يرجى تثبيته باستخدام: pip install pyaudio")
        return
    
    try:
        recorder = ConsoleVoiceRecorder()
        recorder.run()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
