@echo off
title Voice to Text App
echo ========================================
echo       Voice to Text Application
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking dependencies...
python -c "import pyaudio, tkinter" >nul 2>&1
if errorlevel 1 (
    echo Basic dependencies are missing. Installing...
    call install_dependencies.bat
)

echo Checking Whisper installation...
python -c "import whisper" >nul 2>&1
if errorlevel 1 (
    echo.
    echo Whisper is not installed. You have two options:
    echo 1. Run the full app (voice_to_text_app.py) - requires Whisper
    echo 2. Run the simple app (voice_to_text_simple.py) - works without Whisper
    echo.
    set /p choice="Choose option (1 or 2): "
    if "%choice%"=="2" (
        echo Starting simple version...
        python voice_to_text_simple.py
        goto end
    ) else (
        echo Installing Whisper...
        call fix_whisper_installation.bat
    )
)

echo.
echo Starting Voice to Text Application...
echo.
python voice_to_text_app.py

if errorlevel 1 (
    echo.
    echo Application exited with an error.
    pause
)

:end
echo.
echo Application finished.
