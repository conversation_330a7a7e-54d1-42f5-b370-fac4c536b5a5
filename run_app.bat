@echo off
title Voice to Text App
echo ========================================
echo       Voice to Text Application
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking dependencies...
python -c "import whisper, pyaudio, tkinter" >nul 2>&1
if errorlevel 1 (
    echo Some dependencies are missing. Installing...
    call install_dependencies.bat
)

echo.
echo Starting Voice to Text Application...
echo.
python voice_to_text_app.py

if errorlevel 1 (
    echo.
    echo Application exited with an error.
    pause
)
