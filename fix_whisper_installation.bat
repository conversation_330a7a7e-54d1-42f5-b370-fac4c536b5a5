@echo off
echo ========================================
echo Fixing Whisper Installation Issues
echo ========================================

echo.
echo Method 1: Upgrading core packages...
python -m pip install --upgrade pip setuptools wheel

echo.
echo Method 2: Installing dependencies manually...
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install numpy
pip install more-itertools
pip install tiktoken
pip install regex
pip install tqdm

echo.
echo Method 3: Installing Whisper with different approaches...

echo Trying direct installation...
pip install openai-whisper
if errorlevel 1 (
    echo.
    echo Direct installation failed. Trying without dependencies...
    pip install --no-deps openai-whisper
    if errorlevel 1 (
        echo.
        echo Trying from GitHub...
        pip install git+https://github.com/openai/whisper.git
        if errorlevel 1 (
            echo.
            echo Trying with force reinstall...
            pip install --force-reinstall --no-cache-dir openai-whisper
            if errorlevel 1 (
                echo.
                echo All methods failed. Trying manual approach...
                echo Installing from wheel...
                pip install --find-links https://download.pytorch.org/whl/torch_stable.html openai-whisper
            )
        )
    )
)

echo.
echo Installing remaining dependencies...
pip install pyaudio
pip install pyautogui

echo.
echo Testing installation...
python -c "import whisper; print('Whisper installed successfully!')"
if errorlevel 1 (
    echo.
    echo ========================================
    echo Installation verification failed!
    echo ========================================
    echo.
    echo Please try the following manual steps:
    echo 1. pip uninstall openai-whisper
    echo 2. pip install --upgrade pip setuptools wheel
    echo 3. pip install torch torchaudio
    echo 4. pip install openai-whisper
    echo.
) else (
    echo.
    echo ========================================
    echo Installation completed successfully!
    echo ========================================
)

pause
