# تطبيق تحويل الكلام إلى نص - Voice to Text App

تطبيق سطح مكتب لتحويل الكلام إلى نص باستخدام مكتبة Whisper من OpenAI.

## المميزات

- تسجيل الصوت من الميكروفون
- تحويل الكلام إلى نص باستخدام Whisper
- إرسال النص مباشرة إلى أي تطبيق نشط
- واجهة مستخدم بسيطة وسهلة الاستخدام
- دعم اللغة العربية

## متطلبات التشغيل

- Python 3.7 أو أحدث
- ميكروفون متصل بالجهاز
- اتصال بالإنترنت (لتحميل نموذج Whisper في المرة الأولى)

## التثبيت والتشغيل

### الطريقة الأولى (تلقائية):
1. تشغيل ملف `run_app.bat`

### الطريقة الثانية (يدوية):
1. تثبيت المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

2. تشغيل التطبيق:
```bash
python voice_to_text_app.py
```

## كيفية الاستخدام

1. **بدء التطبيق**: تشغيل التطبيق سيقوم بتحميل نموذج Whisper (قد يستغرق بعض الوقت في المرة الأولى)

2. **التسجيل**: 
   - اضغط على زر "ابدأ التسجيل" 🎤
   - تحدث في الميكروفون
   - اضغط على "إيقاف التسجيل" ⏹️ عند الانتهاء

3. **عرض النص**: سيظهر النص المحول في المنطقة المخصصة

4. **استخدام النص**:
   - **نسخ النص**: لنسخ النص إلى الحافظة
   - **إرسال النص**: لكتابة النص مباشرة في التطبيق النشط
   - **مسح**: لمسح النص المعروض

## ملاحظات مهمة

- تأكد من أن الميكروفون يعمل بشكل صحيح
- في المرة الأولى، سيتم تحميل نموذج Whisper (حوالي 140 MB)
- للحصول على أفضل النتائج، تحدث بوضوح وفي بيئة هادئة
- عند استخدام "إرسال النص"، تأكد من أن المؤشر في المكان المناسب في التطبيق المستهدف

## استكشاف الأخطاء

### مشكلة في الميكروفون:
- تأكد من أن الميكروفون متصل ويعمل
- تحقق من إعدادات الصوت في Windows

### مشكلة في تثبيت المكتبات:
- تأكد من تثبيت Python بشكل صحيح
- قم بتشغيل Command Prompt كمدير (Run as Administrator)

### مشكلة في تحميل Whisper:
- تأكد من وجود اتصال بالإنترنت
- قد تحتاج إلى تثبيت PyTorch يدوياً

## المكتبات المستخدمة

- **openai-whisper**: لتحويل الكلام إلى نص
- **pyaudio**: لتسجيل الصوت
- **tkinter**: لواجهة المستخدم الرسومية
- **pyautogui**: لإرسال النص إلى التطبيقات الأخرى
