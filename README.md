# تطبيق تحويل الكلام إلى نص - Voice to Text App

تطبيق سطح مكتب لتحويل الكلام إلى نص باستخدام مكتبة Whisper من OpenAI.

## المميزات

- تسجيل الصوت من الميكروفون
- تحويل الكلام إلى نص باستخدام Whisper
- إرسال النص مباشرة إلى أي تطبيق نشط
- واجهة مستخدم بسيطة وسهلة الاستخدام
- دعم اللغة العربية

## متطلبات التشغيل

- Python 3.7 أو أحدث
- ميكروفون متصل بالجهاز
- اتصال بالإنترنت (لتحميل نموذج Whisper في المرة الأولى)

## التثبيت والتشغيل

### الطريقة الأولى (تلقائية - مستحسنة):
```bash
run_app.bat
```

### الطريقة الثانية (النسخة البسيطة - في حالة مشاكل Whisper):
```bash
run_simple_app.bat
```

### الطريقة الثالثة (إصلاح مشاكل Whisper):
```bash
fix_whisper_installation.bat
```

### الطريقة الرابعة (يدوية):
1. تثبيت المكتبات الأساسية:
```bash
pip install pyaudio pyautogui numpy
```

2. تثبيت Whisper:
```bash
pip install openai-whisper
```

3. تشغيل التطبيق:
```bash
python voice_to_text_app.py
```

## كيفية الاستخدام

1. **بدء التطبيق**: تشغيل التطبيق سيقوم بتحميل نموذج Whisper (قد يستغرق بعض الوقت في المرة الأولى)

2. **التسجيل**: 
   - اضغط على زر "ابدأ التسجيل" 🎤
   - تحدث في الميكروفون
   - اضغط على "إيقاف التسجيل" ⏹️ عند الانتهاء

3. **عرض النص**: سيظهر النص المحول في المنطقة المخصصة

4. **استخدام النص**:
   - **نسخ النص**: لنسخ النص إلى الحافظة
   - **إرسال النص**: لكتابة النص مباشرة في التطبيق النشط
   - **مسح**: لمسح النص المعروض

## ملاحظات مهمة

- تأكد من أن الميكروفون يعمل بشكل صحيح
- في المرة الأولى، سيتم تحميل نموذج Whisper (حوالي 140 MB)
- للحصول على أفضل النتائج، تحدث بوضوح وفي بيئة هادئة
- عند استخدام "إرسال النص"، تأكد من أن المؤشر في المكان المناسب في التطبيق المستهدف

## استكشاف الأخطاء

### مشكلة في الميكروفون:
- تأكد من أن الميكروفون متصل ويعمل
- تحقق من إعدادات الصوت في Windows

### مشكلة في تثبيت المكتبات:
- تأكد من تثبيت Python بشكل صحيح
- قم بتشغيل Command Prompt كمدير (Run as Administrator)

### مشكلة في تحميل Whisper:
- تأكد من وجود اتصال بالإنترنت
- قد تحتاج إلى تثبيت PyTorch يدوياً
- استخدم `fix_whisper_installation.bat` لحل المشاكل
- في حالة فشل التثبيت، استخدم النسخة البسيطة `voice_to_text_simple.py`

### مشكلة "Could not find platform independent libraries":
هذه مشكلة شائعة مع Whisper. الحلول:
1. تشغيل `fix_whisper_installation.bat`
2. أو استخدام النسخة البسيطة: `run_simple_app.bat`
3. أو التثبيت اليدوي:
   ```bash
   pip install --upgrade pip setuptools wheel
   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu
   pip install openai-whisper
   ```

## المكتبات المستخدمة

- **openai-whisper**: لتحويل الكلام إلى نص
- **pyaudio**: لتسجيل الصوت
- **tkinter**: لواجهة المستخدم الرسومية
- **pyautogui**: لإرسال النص إلى التطبيقات الأخرى
