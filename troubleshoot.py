#!/usr/bin/env python3
"""
أداة استكشاف الأخطاء لتطبيق تحويل الكلام إلى نص
"""

import sys
import subprocess
import importlib

def check_python_version():
    """فحص إصدار Python"""
    print("=== فحص إصدار Python ===")
    print(f"إصدار Python: {sys.version}")
    
    if sys.version_info < (3, 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        return False
    else:
        print("✅ إصدار Python مناسب")
        return True

def check_package(package_name, import_name=None):
    """فحص وجود مكتبة معينة"""
    if import_name is None:
        import_name = package_name

    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} مثبت")
        return True
    except ImportError:
        print(f"❌ {package_name} غير مثبت")
        return False
    except Exception as e:
        print(f"❌ {package_name} مثبت لكن به مشكلة: {str(e)}")
        if "whisper" in package_name.lower():
            print("   💡 قد تكون هناك مشكلة في تثبيت Whisper")
        return False

def check_packages():
    """فحص جميع المكتبات المطلوبة"""
    print("\n=== فحص المكتبات المطلوبة ===")
    
    packages = [
        ("whisper", "whisper"),
        ("pyaudio", "pyaudio"),
        ("tkinter", "tkinter"),
        ("pyautogui", "pyautogui"),
        ("numpy", "numpy"),
        ("torch", "torch"),
    ]
    
    all_installed = True
    missing_packages = []
    
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            all_installed = False
            missing_packages.append(package_name)
    
    if not all_installed:
        print(f"\n❌ المكتبات المفقودة: {', '.join(missing_packages)}")
        print("لتثبيت المكتبات المفقودة، قم بتشغيل:")
        print("pip install " + " ".join(missing_packages))
    else:
        print("\n✅ جميع المكتبات مثبتة")
    
    return all_installed

def check_audio_devices():
    """فحص أجهزة الصوت"""
    print("\n=== فحص أجهزة الصوت ===")
    
    try:
        import pyaudio
        
        audio = pyaudio.PyAudio()
        device_count = audio.get_device_count()
        
        print(f"عدد أجهزة الصوت المتاحة: {device_count}")
        
        input_devices = []
        for i in range(device_count):
            device_info = audio.get_device_info_by_index(i)
            if device_info['maxInputChannels'] > 0:
                input_devices.append(device_info['name'])
                print(f"  - {device_info['name']}")
        
        audio.terminate()
        
        if input_devices:
            print("✅ تم العثور على أجهزة إدخال صوتي")
            return True
        else:
            print("❌ لم يتم العثور على أجهزة إدخال صوتي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص أجهزة الصوت: {e}")
        return False

def test_whisper():
    """اختبار تحميل نموذج Whisper"""
    print("\n=== اختبار Whisper ===")
    
    try:
        import whisper
        print("جاري تحميل نموذج Whisper الأساسي...")
        model = whisper.load_model("base")
        print("✅ تم تحميل نموذج Whisper بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في تحميل Whisper: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("أداة استكشاف الأخطاء لتطبيق تحويل الكلام إلى نص")
    print("=" * 50)
    
    # فحص إصدار Python
    python_ok = check_python_version()
    
    # فحص المكتبات
    packages_ok = check_packages()
    
    # فحص أجهزة الصوت
    audio_ok = check_audio_devices()
    
    # اختبار Whisper
    whisper_ok = test_whisper() if packages_ok else False
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    print("ملخص النتائج:")
    print(f"Python: {'✅' if python_ok else '❌'}")
    print(f"المكتبات: {'✅' if packages_ok else '❌'}")
    print(f"أجهزة الصوت: {'✅' if audio_ok else '❌'}")
    print(f"Whisper: {'✅' if whisper_ok else '❌'}")
    
    if all([python_ok, packages_ok, audio_ok, whisper_ok]):
        print("\n🎉 جميع الفحوصات نجحت! يمكنك تشغيل التطبيق الآن.")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى حل قبل تشغيل التطبيق.")
        
        if not packages_ok:
            print("\nلحل مشاكل المكتبات:")
            print("1. قم بتشغيل: install_dependencies.bat")
            print("2. أو قم بتشغيل: pip install -r requirements.txt")
        
        if not audio_ok:
            print("\nلحل مشاكل الصوت:")
            print("1. تأكد من توصيل الميكروفون")
            print("2. تحقق من إعدادات الصوت في Windows")
            print("3. قم بإعادة تشغيل التطبيق")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
