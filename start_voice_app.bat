@echo off
title Voice to Text Application Launcher
color 0A
echo.
echo ========================================
echo    Voice to Text Application Launcher
echo ========================================
echo.
echo تم اكتشاف مشاكل في تثبيت Whisper و tkinter
echo لديك عدة خيارات للتشغيل:
echo.
echo ========================================
echo الخيارات المتاحة:
echo ========================================
echo.
echo 1. تطبيق التسجيل البسيط (سطر الأوامر)
echo    - يعمل بدون مشاكل
echo    - تسجيل صوتي فقط
echo    - لا يحتاج Whisper أو واجهة رسومية
echo.
echo 2. تنظيف وإعادة تثبيت جميع المكتبات
echo    - حل شامل لجميع المشاكل
echo    - قد يستغرق وقت طويل
echo    - مستحسن للحل النهائي
echo.
echo 3. إصلاح تثبيت Whisper فقط
echo    - محاولة إصلاح Whisper
echo    - أسرع من الخيار 2
echo.
echo 4. تشغيل أداة استكشاف الأخطاء
echo    - فحص تفصيلي للمشاكل
echo    - تقرير شامل
echo.
echo 5. خروج
echo.
echo ========================================

set /p choice="اختر رقم الخيار (1-5): "

if "%choice%"=="1" (
    echo.
    echo تشغيل تطبيق التسجيل البسيط...
    echo.
    python voice_recorder_console.py
    goto end
)

if "%choice%"=="2" (
    echo.
    echo بدء عملية التنظيف وإعادة التثبيت...
    echo هذا قد يستغرق عدة دقائق...
    echo.
    call clean_and_reinstall.bat
    goto end
)

if "%choice%"=="3" (
    echo.
    echo محاولة إصلاح تثبيت Whisper...
    echo.
    call fix_whisper_installation.bat
    goto end
)

if "%choice%"=="4" (
    echo.
    echo تشغيل أداة استكشاف الأخطاء...
    echo.
    python troubleshoot.py
    goto end
)

if "%choice%"=="5" (
    echo.
    echo شكراً لاستخدام التطبيق!
    goto end
)

echo.
echo خيار غير صحيح. يرجى المحاولة مرة أخرى.
pause
goto start

:end
echo.
echo ========================================
echo انتهى التطبيق
echo ========================================
pause
