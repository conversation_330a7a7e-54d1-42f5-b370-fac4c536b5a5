import tkinter as tk
from tkinter import ttk, messagebox
import threading
import pyaudio
import wave
import pyautogui
import os
import tempfile
import time

# محاولة استيراد Whisper مع معالجة الأخطاء
WHISPER_AVAILABLE = False
WHISPER_ERROR = None
try:
    import whisper
    WHISPER_AVAILABLE = True
    print("✅ Whisper متاح")
except ImportError as e:
    WHISPER_ERROR = f"ImportError: {str(e)}"
    print("❌ Whisper غير متاح - سيتم استخدام وضع التسجيل فقط")
except Exception as e:
    WHISPER_ERROR = f"Error: {str(e)}"
    print(f"❌ Whisper مثبت لكن به مشكلة: {str(e)}")
    print("سيتم استخدام وضع التسجيل فقط")

class SimpleVoiceToTextApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تحويل الكلام إلى نص - Voice to Text (Simple)")
        self.root.geometry("400x350")
        self.root.resizable(False, False)
        
        # متغيرات التسجيل
        self.is_recording = False
        self.audio_frames = []
        self.audio_stream = None
        self.audio = None
        
        # إعدادات الصوت
        self.CHUNK = 1024
        self.FORMAT = pyaudio.paInt16
        self.CHANNELS = 1
        self.RATE = 16000
        
        # نموذج Whisper
        self.whisper_model = None
        
        self.setup_ui()
        
        if WHISPER_AVAILABLE:
            self.load_whisper_model()
        else:
            self.status_label.config(text="Whisper غير متاح - وضع التسجيل فقط")
            
    def load_whisper_model(self):
        """تحميل نموذج Whisper"""
        def load_model():
            try:
                self.status_label.config(text="جاري تحميل نموذج Whisper...")
                self.root.update()
                self.whisper_model = whisper.load_model("base")
                self.status_label.config(text="جاهز للاستخدام")
                self.record_button.config(state="normal")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل نموذج Whisper: {str(e)}")
                self.status_label.config(text="خطأ في تحميل النموذج")
        
        self.record_button.config(state="disabled")
        threading.Thread(target=load_model, daemon=True).start()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(self.root, text="تحويل الكلام إلى نص", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=20)
        
        # حالة التطبيق
        self.status_label = tk.Label(self.root, text="جاري التحميل...", 
                                   font=("Arial", 10))
        self.status_label.pack(pady=5)
        
        # تحذير إذا لم يكن Whisper متاحاً
        if not WHISPER_AVAILABLE:
            if WHISPER_ERROR and "TypeError" in WHISPER_ERROR:
                warning_text = "⚠️ مشكلة في تثبيت Whisper - يمكنك التسجيل فقط"
                error_label = tk.Label(self.root,
                                     text="💡 استخدم clean_and_reinstall.bat لحل المشكلة",
                                     font=("Arial", 9), fg="blue")
                error_label.pack(pady=2)
            else:
                warning_text = "⚠️ Whisper غير مثبت - يمكنك التسجيل فقط"

            warning_label = tk.Label(self.root,
                                   text=warning_text,
                                   font=("Arial", 10), fg="orange")
            warning_label.pack(pady=5)
        
        # زر التسجيل
        self.record_button = tk.Button(self.root, text="🎤 ابدأ التسجيل", 
                                     font=("Arial", 14), 
                                     bg="#4CAF50", fg="white",
                                     command=self.toggle_recording,
                                     width=15, height=2)
        self.record_button.pack(pady=20)
        
        # مؤشر التسجيل
        self.recording_indicator = tk.Label(self.root, text="", 
                                          font=("Arial", 12), fg="red")
        self.recording_indicator.pack(pady=5)
        
        # منطقة النص
        text_frame = tk.Frame(self.root)
        text_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        tk.Label(text_frame, text="النص المحول:", font=("Arial", 10)).pack(anchor="w")
        
        self.text_display = tk.Text(text_frame, height=6, wrap=tk.WORD, 
                                  font=("Arial", 10))
        self.text_display.pack(fill="both", expand=True)
        
        # أزرار
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        self.copy_button = tk.Button(button_frame, text="نسخ النص", 
                                   command=self.copy_text)
        self.copy_button.pack(side="left", padx=5)
        
        self.send_button = tk.Button(button_frame, text="إرسال النص", 
                                   command=self.send_text)
        self.send_button.pack(side="left", padx=5)
        
        self.clear_button = tk.Button(button_frame, text="مسح", 
                                    command=self.clear_text)
        self.clear_button.pack(side="left", padx=5)
        
        # أزرار تثبيت Whisper
        if not WHISPER_AVAILABLE:
            button_frame2 = tk.Frame(self.root)
            button_frame2.pack(pady=5)

            if WHISPER_ERROR and "TypeError" in WHISPER_ERROR:
                clean_button = tk.Button(button_frame2, text="تنظيف وإعادة تثبيت",
                                       command=self.clean_and_reinstall,
                                       bg="#FF5722", fg="white")
                clean_button.pack(side="left", padx=5)

            install_button = tk.Button(button_frame2, text="تثبيت Whisper",
                                     command=self.install_whisper,
                                     bg="#2196F3", fg="white")
            install_button.pack(side="left", padx=5)
        
    def toggle_recording(self):
        """تبديل حالة التسجيل"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
            
    def start_recording(self):
        """بدء التسجيل"""
        try:
            self.audio = pyaudio.PyAudio()
            self.audio_stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                frames_per_buffer=self.CHUNK
            )
            
            self.is_recording = True
            self.audio_frames = []
            
            self.record_button.config(text="⏹️ إيقاف التسجيل", bg="#f44336")
            self.recording_indicator.config(text="🔴 جاري التسجيل...")
            self.status_label.config(text="جاري التسجيل...")
            
            self.recording_thread = threading.Thread(target=self.record_audio)
            self.recording_thread.start()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في بدء التسجيل: {str(e)}")
            
    def record_audio(self):
        """تسجيل الصوت"""
        while self.is_recording:
            try:
                data = self.audio_stream.read(self.CHUNK)
                self.audio_frames.append(data)
            except Exception as e:
                print(f"خطأ في التسجيل: {e}")
                break
                
    def stop_recording(self):
        """إيقاف التسجيل"""
        self.is_recording = False
        
        if self.audio_stream:
            self.audio_stream.stop_stream()
            self.audio_stream.close()
        if self.audio:
            self.audio.terminate()
            
        self.record_button.config(text="🎤 ابدأ التسجيل", bg="#4CAF50")
        self.recording_indicator.config(text="")
        
        if WHISPER_AVAILABLE and self.whisper_model:
            self.status_label.config(text="جاري معالجة الصوت...")
            threading.Thread(target=self.process_audio).start()
        else:
            self.save_audio_file()
            
    def process_audio(self):
        """معالجة الصوت وتحويله إلى نص"""
        try:
            if not self.audio_frames:
                self.status_label.config(text="لم يتم تسجيل أي صوت")
                return
                
            # حفظ الصوت في ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
            
            with wave.open(temp_file.name, 'wb') as wf:
                wf.setnchannels(self.CHANNELS)
                wf.setsampwidth(self.audio.get_sample_size(self.FORMAT))
                wf.setframerate(self.RATE)
                wf.writeframes(b''.join(self.audio_frames))
            
            # تحويل الصوت إلى نص
            if self.whisper_model:
                result = self.whisper_model.transcribe(temp_file.name, language="ar")
                text = result["text"].strip()
                
                if text:
                    self.text_display.delete(1.0, tk.END)
                    self.text_display.insert(1.0, text)
                    self.status_label.config(text="تم التحويل بنجاح")
                else:
                    self.status_label.config(text="لم يتم التعرف على أي كلام")
            
            os.unlink(temp_file.name)
            
        except Exception as e:
            self.status_label.config(text=f"خطأ في المعالجة: {str(e)}")
            
    def save_audio_file(self):
        """حفظ ملف الصوت عند عدم توفر Whisper"""
        try:
            if not self.audio_frames:
                self.status_label.config(text="لم يتم تسجيل أي صوت")
                return
                
            filename = f"recording_{int(time.time())}.wav"
            
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(self.CHANNELS)
                wf.setsampwidth(self.audio.get_sample_size(self.FORMAT))
                wf.setframerate(self.RATE)
                wf.writeframes(b''.join(self.audio_frames))
            
            self.status_label.config(text=f"تم حفظ التسجيل: {filename}")
            self.text_display.delete(1.0, tk.END)
            self.text_display.insert(1.0, f"تم حفظ التسجيل في الملف: {filename}\n\nلتحويل الكلام إلى نص، يرجى تثبيت Whisper أولاً.")
            
        except Exception as e:
            self.status_label.config(text=f"خطأ في الحفظ: {str(e)}")
            
    def copy_text(self):
        """نسخ النص إلى الحافظة"""
        text = self.text_display.get(1.0, tk.END).strip()
        if text:
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            self.status_label.config(text="تم نسخ النص")
        else:
            messagebox.showwarning("تحذير", "لا يوجد نص للنسخ")
            
    def send_text(self):
        """إرسال النص إلى التطبيق النشط"""
        text = self.text_display.get(1.0, tk.END).strip()
        if text:
            try:
                self.root.withdraw()
                time.sleep(0.5)
                pyautogui.typewrite(text)
                self.root.deiconify()
                self.status_label.config(text="تم إرسال النص")
            except Exception as e:
                self.root.deiconify()
                messagebox.showerror("خطأ", f"فشل في إرسال النص: {str(e)}")
        else:
            messagebox.showwarning("تحذير", "لا يوجد نص للإرسال")
            
    def clear_text(self):
        """مسح النص"""
        self.text_display.delete(1.0, tk.END)
        self.status_label.config(text="تم مسح النص")
        
    def clean_and_reinstall(self):
        """تنظيف وإعادة تثبيت جميع المكتبات"""
        messagebox.showinfo("تنظيف وإعادة تثبيت",
                          "سيتم تنظيف وإعادة تثبيت جميع المكتبات.\n"
                          "هذا قد يستغرق عدة دقائق.\n"
                          "يرجى انتظار انتهاء العملية ثم إعادة تشغيل التطبيق.")
        os.system("start clean_and_reinstall.bat")

    def install_whisper(self):
        """تثبيت Whisper"""
        messagebox.showinfo("تثبيت Whisper",
                          "سيتم فتح نافذة لتثبيت Whisper.\nيرجى انتظار انتهاء التثبيت ثم إعادة تشغيل التطبيق.")
        os.system("start fix_whisper_installation.bat")

def main():
    root = tk.Tk()
    app = SimpleVoiceToTextApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
