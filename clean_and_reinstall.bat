@echo off
echo ========================================
echo   Clean Installation for Voice to Text
echo ========================================
echo.
echo This script will clean and reinstall all dependencies
echo to fix the Whisper installation issues.
echo.
pause

echo Step 1: Cleaning existing installations...
pip uninstall -y openai-whisper whisper torch torchaudio
pip uninstall -y pyaudio pyautogui numpy

echo.
echo Step 2: Clearing pip cache...
pip cache purge

echo.
echo Step 3: Upgrading pip and tools...
python -m pip install --upgrade pip setuptools wheel

echo.
echo Step 4: Installing PyTorch first (CPU version)...
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu

echo.
echo Step 5: Installing basic dependencies...
pip install numpy

echo.
echo Step 6: Installing audio dependencies...
pip install pyaudio

echo.
echo Step 7: Installing GUI dependencies...
pip install pyautogui

echo.
echo Step 8: Installing Whisper with specific method...
echo Trying method 1: Direct installation...
pip install openai-whisper
if errorlevel 1 (
    echo Method 1 failed. Trying method 2: From source...
    pip install git+https://github.com/openai/whisper.git
    if errorlevel 1 (
        echo Method 2 failed. Trying method 3: Alternative package...
        pip install whisper-openai
        if errorlevel 1 (
            echo All methods failed. Installing minimal dependencies only...
            echo You can use the simple version without Whisper.
        )
    )
)

echo.
echo Step 9: Testing installation...
python -c "
try:
    import torch
    print('✅ PyTorch: OK')
except:
    print('❌ PyTorch: Failed')

try:
    import pyaudio
    print('✅ PyAudio: OK')
except:
    print('❌ PyAudio: Failed')

try:
    import pyautogui
    print('✅ PyAutoGUI: OK')
except:
    print('❌ PyAutoGUI: Failed')

try:
    import whisper
    print('✅ Whisper: OK')
except Exception as e:
    print(f'❌ Whisper: Failed - {e}')
"

echo.
echo ========================================
echo Installation completed!
echo ========================================
echo.
echo If Whisper still doesn't work, use the simple version:
echo python voice_to_text_simple.py
echo.
pause
